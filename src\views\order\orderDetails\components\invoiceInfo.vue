<template>
    <div class="invoice-info" v-loading="loading">
        <!-- 基本信息卡片 -->
        <div class="info-card">
            <div class="card-header">
                <h3>基本信息</h3>
                <div class="action-buttons">
                    <el-button type="primary" size="small">申请开票</el-button>
                    <el-button size="small">下载发票</el-button>
                </div>
            </div>
            <div class="card-content">
                <div class="info-row">
                    <div class="info-item">
                        <span class="label">订单号：</span>
                        <span class="value">{{ orderNo }}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">开票金额：</span>
                        <span class="value amount">¥{{ formatMoney(invoiceData.invoiceAmount) }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 发票详情卡片 -->
        <div class="info-card" v-if="invoiceData.id">
            <div class="card-header">
                <h3>发票详情</h3>
            </div>
            <div class="card-content">
                <div class="detail-grid">
                    <div class="detail-row">
                        <div class="detail-item">
                            <span class="label">【商家/供应商】</span>
                            <span class="value">{{ invoiceData.shopSupplierName }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">金额：</span>
                            <span class="value amount">¥{{ formatMoney(invoiceData.invoiceAmount) }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">抬头类型：</span>
                            <span class="value">{{ getHeaderTypeLabel(invoiceData.invoiceHeaderType) }}</span>
                        </div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-item">
                            <span class="label">发票类型：</span>
                            <span class="value">{{ getInvoiceTypeLabel(invoiceData.invoiceType) }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">税号编码：</span>
                            <span class="value">{{ invoiceData.taxIdentNo }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">发票抬头：</span>
                            <span class="value">{{ invoiceData.header }}</span>
                        </div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-item">
                            <span class="label">邮箱地址：</span>
                            <span class="value">{{ invoiceData.email }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">开户行：</span>
                            <span class="value">{{ invoiceData.openingBank }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">银行账号：</span>
                            <span class="value">{{ invoiceData.bankAccountNo }}</span>
                        </div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-item">
                            <span class="label">企业电话：</span>
                            <span class="value">{{ invoiceData.enterprisePhone }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">企业地址：</span>
                            <span class="value">{{ invoiceData.enterpriseAddress }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">申请时间：</span>
                            <span class="value">{{ formatDateTime(invoiceData.applicationTime) }}</span>
                        </div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-item full-width">
                            <span class="label">开票状态：</span>
                            <span class="value status" :class="getStatusClass(invoiceData.invoiceStatus)">
                                {{ getStatusLabel(invoiceData.invoiceStatus) }}
                            </span>
                        </div>
                    </div>

                    <div class="detail-row" v-if="invoiceData.billingRemarks">
                        <div class="detail-item full-width">
                            <span class="label">拒绝原因或备注信息：</span>
                            <span class="value">{{ invoiceData.billingRemarks }}</span>
                        </div>
                    </div>
                </div>

                <!-- 底部操作按钮 -->
                <div class="card-footer">
                    <div class="footer-buttons">
                        <el-button size="small" @click="handleCancelApplication">撤销申请</el-button>
                        <el-button type="primary" size="small" @click="handleInvoiceGoods">开票商品</el-button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && !invoiceData.id" class="empty-state">
            <el-empty description="暂无发票申请记录" />
        </div>
    </div>
</template>

<script setup lang="ts" name="InvoiceInfo">
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { doGetinvoiceRequestList } from '@/apis/invoice'
import type { ApiOrder } from '@/views/order/types/order'

interface Props {
    order?: ApiOrder
}

const props = defineProps<Props>()
const $route = useRoute()

// 响应式数据
const loading = ref(false)
const invoiceData = ref<any>({})

// 获取订单号
const orderNo = computed(() => {
    return props.order?.no || ($route.query.orderNo as string) || ''
})

/**
 * 加载发票申请记录
 */
const loadInvoiceData = async () => {
    if (!orderNo.value) {
        ElMessage.warning('订单号不能为空')
        return
    }

    try {
        loading.value = true
        console.log('查询发票申请记录，订单号:', orderNo.value)

        const params = {
            orderNo: orderNo.value,
            applyType: 'OWNER', // 申请类型
            invoiceOwnerType: 'SUPPLIER', // 发票所属方类型（供应商端）
            current: 1,
            size: 1, // 只获取第一条记录
        }

        console.log('发票申请查询参数:', params)
        console.log('接口地址: http://192.168.1.254:9999/addon-invoice/invoice/invoiceRequest')

        const { code, data, msg } = await doGetinvoiceRequestList(params)

        if (code === 200 && data) {
            console.log('发票申请记录查询成功:', data)
            const records = data.records || data || []
            if (records.length > 0) {
                invoiceData.value = records[0] // 取第一条记录
            } else {
                invoiceData.value = {}
            }
        } else {
            console.error('发票申请记录查询失败:', msg)
            ElMessage.error(msg || '查询发票申请记录失败')
            invoiceData.value = {}
        }
    } catch (error) {
        console.error('查询发票申请记录异常:', error)
        ElMessage.error('查询发票申请记录失败，请稍后重试')
        invoiceData.value = {}
    } finally {
        loading.value = false
    }
}

/**
 * 格式化金额显示
 */
const formatMoney = (money: string | number) => {
    if (!money) return '0.00'
    const amount = typeof money === 'string' ? parseFloat(money) : money
    return (amount / 100).toFixed(2) // 假设后端返回的是分为单位
}

/**
 * 获取发票类型标签
 */
const getInvoiceTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
        VAT_GENERAL: '增值税电子普通发票',
        VAT_SPECIAL: '增值税电子专用发票',
    }
    return labels[type] || type || '增值税电子普通发票'
}

/**
 * 获取抬头类型标签
 */
const getHeaderTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
        PERSONAL: '个人',
        ENTERPRISE: '企业',
        COMPANY: '企业',
    }
    return labels[type] || '个人或企业单位'
}

/**
 * 获取状态标签
 */
const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
        REQUEST_IN_PROCESS: '开票中',
        SUCCESSFULLY_INVOICED: '开票成功',
        FAILED_INVOICE_REQUEST: '开票失败',
        CLIENT_CANCEL_REQUEST: '用户撤销',
    }
    return labels[status] || status || '开票中/开票成功/开票失败'
}

/**
 * 获取状态样式类
 */
const getStatusClass = (status: string) => {
    const classes: Record<string, string> = {
        REQUEST_IN_PROCESS: 'status-processing',
        SUCCESSFULLY_INVOICED: 'status-success',
        FAILED_INVOICE_REQUEST: 'status-failed',
        CLIENT_CANCEL_REQUEST: 'status-cancelled',
    }
    return classes[status] || 'status-processing'
}

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime: string) => {
    if (!dateTime) return ''
    return new Date(dateTime).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
    })
}

/**
 * 撤销申请
 */
const handleCancelApplication = () => {
    if (!invoiceData.value.id) {
        ElMessage.warning('暂无发票信息')
        return
    }

    // 只有开票中的状态才能撤销
    if (invoiceData.value.invoiceStatus !== 'REQUEST_IN_PROCESS') {
        ElMessage.warning('只有开票中的申请才能撤销')
        return
    }

    ElMessage.info('撤销申请功能开发中...')
    // TODO: 实现撤销申请的逻辑
}

/**
 * 开票商品
 */
const handleInvoiceGoods = () => {
    if (!invoiceData.value.id) {
        ElMessage.warning('暂无发票信息')
        return
    }

    // 跳转到开票商品页面或显示商品详情
    ElMessage.info('开票商品功能开发中...')
    // TODO: 实现查看开票商品的逻辑
}

// 组件挂载时加载数据
onMounted(() => {
    loadInvoiceData()
})
</script>

<style scoped lang="scss">
.invoice-info {
    padding: 20px;
    background: #f5f7fa;
    min-height: 500px;

    .info-card {
        background: #fff;
        border-radius: 8px;
        margin-bottom: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid #eee;

            h3 {
                margin: 0;
                color: #333;
                font-size: 16px;
                font-weight: 600;
            }

            .action-buttons {
                display: flex;
                gap: 8px;
            }
        }

        .card-content {
            padding: 20px;

            .info-row {
                display: flex;
                gap: 40px;

                .info-item {
                    display: flex;
                    align-items: center;

                    .label {
                        color: #666;
                        font-size: 14px;
                        margin-right: 8px;
                    }

                    .value {
                        color: #333;
                        font-size: 14px;

                        &.amount {
                            color: #f56c6c;
                            font-weight: 600;
                        }
                    }
                }
            }

            .detail-grid {
                .detail-row {
                    display: flex;
                    margin-bottom: 16px;
                    gap: 40px;

                    .detail-item {
                        flex: 1;
                        display: flex;
                        align-items: center;

                        &.full-width {
                            flex: 3;
                        }

                        .label {
                            color: #666;
                            font-size: 14px;
                            margin-right: 8px;
                            min-width: 80px;
                        }

                        .value {
                            color: #333;
                            font-size: 14px;

                            &.amount {
                                color: #f56c6c;
                                font-weight: 600;
                            }

                            &.status {
                                &.status-processing {
                                    color: #e6a23c;
                                }

                                &.status-success {
                                    color: #67c23a;
                                }

                                &.status-failed {
                                    color: #f56c6c;
                                }

                                &.status-cancelled {
                                    color: #909399;
                                }
                            }
                        }
                    }
                }
            }
        }

        .card-footer {
            padding: 16px 20px;
            border-top: 1px solid #f0f0f0;
            background: #fafafa;
            border-radius: 0 0 8px 8px;

            .footer-buttons {
                display: flex;
                justify-content: flex-end;
                gap: 12px;

                .el-button {
                    min-width: 80px;
                    border-radius: 4px;
                    font-size: 13px;

                    &:not(.el-button--primary) {
                        background: #fff;
                        border-color: #d9d9d9;
                        color: #666;

                        &:hover {
                            border-color: #409eff;
                            color: #409eff;
                        }
                    }

                    &.el-button--primary {
                        background: #409eff;
                        border-color: #409eff;

                        &:hover {
                            background: #66b1ff;
                            border-color: #66b1ff;
                        }
                    }
                }
            }
        }
    }

    .empty-state {
        background: #fff;
        border-radius: 8px;
        padding: 40px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
}
</style>
